// Company logos data
const COMPANY_LOGOS = [
  {
    name: "AccuWeat<PERSON>",
    src: "https://upload.wikimedia.org/wikipedia/commons/2/2e/AccuWeather_Logo.svg",
    height: "h-10",
    invert: false
  },
  {
    name: "CA Technologies",
    src: "https://upload.wikimedia.org/wikipedia/commons/0/08/CA_Technologies_logo.svg",
    height: "h-8",
    invert: true
  },
  {
    name: "<PERSON>",
    src: "https://upload.wikimedia.org/wikipedia/commons/4/48/Dell_Logo.svg",
    height: "h-8",
    invert: true
  },
  {
    name: "Amazon",
    src: "https://www.freepnglogos.com/uploads/amazon-png-logo-vector/woodland-gardening-amazon-png-logo-vector-8.png",
    height: "h-8",
    invert: true
  },
  {
    name: "Hyatt",
    src: "https://upload.wikimedia.org/wikipedia/commons/9/91/Hyatt_Logo.svg",
    height: "h-8",
    invert: true
  },
  {
    name: "LifeTouch",
    src: "https://upload.wikimedia.org/wikipedia/commons/9/9a/Lifetouch_logo.svg",
    height: "h-8",
    invert: false
  },
  {
    name: "Krispy Kreme",
    src: "https://upload.wikimedia.org/wikipedia/commons/8/8b/Logo.KrispyKreme.svg",
    height: "h-8",
    invert: true
  },
  {
    name: "Ray-Ban",
    src: "https://upload.wikimedia.org/wikipedia/commons/7/7b/Ray-Ban_logo_2.svg",
    height: "h-8",
    invert: false
  }
] as const;

// Reusable logo component
interface LogoProps {
  name: string;
  src: string;
  height: string;
  invert: boolean;
}

const Logo = ({ name, src, height, invert }: LogoProps) => (
  <div className="flex items-center justify-center">
    <img
      src={src}
      alt={name}
      loading="lazy"
      className={`${height} w-auto object-contain opacity-70 hover:opacity-100 transition-opacity ${invert ? 'invert' : ''}`}
    />
  </div>
);

const CompanyLogos = () => {
  return (
    <section className="relative overflow-hidden py-20 md:py-24">
      <div className="container relative z-20 mx-auto px-4 md:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-2xl md:text-3xl font-semibold mb-6 leading-tight tracking-tight text-white">
            Used by teams of <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">leading companies</span>
          </h2>
        </div>

        {/* Company logos - White versions */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12 max-w-5xl mx-auto">
          {COMPANY_LOGOS.map((logo, index) => (
            <Logo
              key={index}
              name={logo.name}
              src={logo.src}
              height={logo.height}
              invert={logo.invert}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default CompanyLogos;