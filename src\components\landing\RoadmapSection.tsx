import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, ArrowR<PERSON>, Sparkles, Mic, Wand2, Video } from "lucide-react";

const RoadmapSection = () => {
  const navigate = useNavigate();

  return (
    <section className="relative overflow-hidden py-24 md:py-32">
      <div className="container relative z-20 mx-auto px-4 md:px-8">
        {/* Section header */}
        <div className="text-center mb-16 md:mb-24">
          <div className="inline-block mb-6 px-4 py-1.5 bg-white/10 backdrop-blur-md text-white/80 rounded-full text-sm font-medium">
            <span className="flex items-center gap-2">
              <Calendar className="w-3.5 h-3.5" />
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Roadmap</span>
            </span>
          </div>

          <h2 className="text-3xl md:text-5xl font-semibold mb-6 leading-tight tracking-tight text-white">
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Coming Soon</span> to VibeNecto
          </h2>

          <p className="text-lg text-white/80 mb-0 max-w-2xl mx-auto leading-relaxed">
            We're constantly evolving. Here's a sneak peek at what's coming next.
          </p>
        </div>

        {/* Roadmap timeline - Horizontal */}
        <div className="max-w-6xl mx-auto">
          {/* Features grid with proper alignment */}
          <div className="relative">
            {/* Timeline line - Properly positioned to connect dots */}
            <div className="absolute top-6 left-0 right-0 h-0.5 bg-gradient-to-r from-purple-500/50 via-blue-500/50 to-amber-500/50 hidden md:block"></div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
              {/* Feature 1 - Voice Generation */}
              <div className="relative text-center">
                {/* Timeline dot - desktop */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full bg-gradient-to-r from-purple-600/20 to-pink-500/20 border-2 border-purple-500 flex items-center justify-center z-10 hidden md:flex">
                  <div className="w-4 h-4 rounded-full bg-gradient-to-r from-purple-600 to-pink-500"></div>
                </div>

                {/* Content container with proper top margin */}
                <div className="pt-16 md:pt-20">
                  {/* Date badge */}
                  <div className="mb-6">
                    <div className="inline-block px-4 py-1.5 bg-purple-500/20 backdrop-blur-md border border-purple-400/30 rounded-full text-sm font-medium">
                      <span className="bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent font-semibold">Q3 2025</span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-purple-600/10 to-pink-600/10 flex items-center justify-center">
                    <Mic className="w-8 h-8 text-purple-400" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4 text-white">Music Generation</h3>
                  <p className="text-white/70 leading-relaxed">
                    Generate custom background music and soundtracks for your marketing videos with AI-powered composition.
                  </p>
                </div>
              </div>

              {/* Feature 2 - Pro Model */}
              <div className="relative text-center">
                {/* Timeline dot - desktop */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full bg-gradient-to-r from-blue-600/20 to-cyan-500/20 border-2 border-blue-500 flex items-center justify-center z-10 hidden md:flex">
                  <div className="w-4 h-4 rounded-full bg-gradient-to-r from-blue-600 to-cyan-500"></div>
                </div>

                {/* Content container with proper top margin */}
                <div className="pt-16 md:pt-20">
                  {/* Date badge */}
                  <div className="mb-6">
                    <div className="inline-block px-4 py-1.5 bg-blue-500/20 backdrop-blur-md border border-blue-400/30 rounded-full text-sm font-medium">
                      <span className="bg-gradient-to-r from-blue-300 to-cyan-300 bg-clip-text text-transparent font-semibold">Q3 2025</span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-blue-600/10 to-cyan-600/10 flex items-center justify-center">
                    <Wand2 className="w-8 h-8 text-blue-400" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4 text-white">Pro Model</h3>
                  <p className="text-white/70 leading-relaxed">
                    Next-level image creation with multi-subject composition, advanced editing, and photorealistic quality.
                  </p>
                </div>
              </div>

              {/* Feature 3 - Ultra Model */}
              <div className="relative text-center">
                {/* Timeline dot - desktop */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full bg-gradient-to-r from-amber-600/20 to-orange-500/20 border-2 border-amber-500 flex items-center justify-center z-10 hidden md:flex">
                  <div className="w-4 h-4 rounded-full bg-gradient-to-r from-amber-600 to-orange-500"></div>
                </div>

                {/* Content container with proper top margin */}
                <div className="pt-16 md:pt-20">
                  {/* Date badge */}
                  <div className="mb-6">
                    <div className="inline-block px-4 py-1.5 bg-amber-500/20 backdrop-blur-md border border-amber-400/30 rounded-full text-sm font-medium">
                      <span className="bg-gradient-to-r from-amber-300 to-orange-300 bg-clip-text text-transparent font-semibold">Q4 2025</span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-gradient-to-br from-amber-600/10 to-orange-600/10 flex items-center justify-center">
                    <Video className="w-8 h-8 text-amber-400" />
                  </div>
                  <h3 className="text-xl font-semibold mb-4 text-white">Ultra Model</h3>
                  <p className="text-white/70 leading-relaxed">
                    Capable of video and sound generation together with state-of-the-art image generation. The ultimate AI creative suite.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* CTA button */}
          <div className="text-center mt-16">
            <Button
              size="lg"
              onClick={() => navigate("/signup")}
              className="bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 text-white hover:from-cyan-400 hover:via-blue-400 hover:to-purple-400 h-12 px-8 rounded-full font-medium shadow-xl"
            >
              <Sparkles className="w-4 h-4 mr-2 text-yellow-300" />
              <span>Join the Waitlist</span>
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RoadmapSection;