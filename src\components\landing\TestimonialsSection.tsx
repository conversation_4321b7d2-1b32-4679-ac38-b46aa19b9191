import { Star, Quote } from "lucide-react";

// Testimonials data
const TESTIMONIALS = [
  {
    id: 1,
    quote: "VibeNecto has completely transformed our social media content creation. We've cut our image production time by 80% while maintaining our brand's unique aesthetic.",
    author: "<PERSON>",
    position: "Marketing Director, Hyatt",
    initials: "S<PERSON>",
    gradientClass: "bg-gradient-to-r from-purple-600/20 to-pink-500/20"
  },
  {
    id: 2,
    quote: "The background removal feature alone is worth the subscription. We've been able to create professional product images in seconds that previously took our design team hours.",
    author: "<PERSON>",
    position: "E-commerce Manager, Ray-<PERSON>",
    initials: "RL",
    gradientClass: "bg-gradient-to-r from-blue-600/20 to-cyan-500/20"
  },
  {
    id: 3,
    quote: "As a small business owner, I don't have the budget for a full design team. VibeNecto gives me the ability to create professional marketing materials that look like they were made by experts.",
    author: "<PERSON>",
    position: "Founder, Bloom Boutique",
    initials: "<PERSON>",
    gradientClass: "bg-gradient-to-r from-amber-600/20 to-orange-500/20"
  }
] as const;

// Reusable testimonial card component
interface TestimonialCardProps {
  quote: string;
  author: string;
  position: string;
  initials: string;
  gradientClass: string;
}

const TestimonialCard = ({ quote, author, position, initials, gradientClass }: TestimonialCardProps) => (
  <div className="relative p-8">
    <Quote className="w-6 h-6 text-white/40 mb-6" />
    <div className="flex mb-4">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star key={star} className="w-4 h-4 text-yellow-400 mr-1" fill="#FBBF24" />
      ))}
    </div>
    <p className="text-white/80 text-lg leading-relaxed mb-8">
      "{quote}"
    </p>
    <div className="flex items-center">
      <div className={`w-12 h-12 rounded-full ${gradientClass} flex items-center justify-center text-white font-medium`}>
        {initials}
      </div>
      <div className="ml-4">
        <p className="text-white font-medium">{author}</p>
        <p className="text-white/60">{position}</p>
      </div>
    </div>
  </div>
);

const TestimonialsSection = () => {
  return (
    <section className="relative overflow-hidden py-24 md:py-32">
      <div className="container relative z-20 mx-auto px-4 md:px-8">
        {/* Section header */}
        <div className="text-center mb-16 md:mb-24">
          <div className="inline-block mb-6 px-4 py-1.5 bg-white/10 backdrop-blur-md text-white/80 rounded-full text-sm font-medium">
            <span className="flex items-center gap-2">
              <Star className="w-3.5 h-3.5 text-yellow-400" />
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Customer Reviews</span>
            </span>
          </div>

          <h2 className="text-3xl md:text-5xl font-semibold mb-6 leading-tight tracking-tight text-white">
            What our <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">users</span> are saying
          </h2>

          <p className="text-lg text-white/80 mb-0 max-w-2xl mx-auto leading-relaxed">
            Discover how VibeNecto is transforming marketing workflows for teams around the world.
          </p>
        </div>

        {/* Reviews grid - Seamless */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {TESTIMONIALS.map((testimonial) => (
            <TestimonialCard
              key={testimonial.id}
              quote={testimonial.quote}
              author={testimonial.author}
              position={testimonial.position}
              initials={testimonial.initials}
              gradientClass={testimonial.gradientClass}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;