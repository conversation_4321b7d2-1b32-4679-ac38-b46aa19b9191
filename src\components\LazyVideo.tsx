import React, { useState, useRef, useEffect } from 'react';
import { AlertTriangle, Play } from 'lucide-react';
import { cn } from '@/lib/utils';
import VideoSkeleton from '@/components/VideoSkeleton';

interface LazyVideoProps {
  src: string;
  alt?: string;
  className?: string;
  onError?: (e: React.SyntheticEvent<HTMLVideoElement, Event>) => void;
  onClick?: (e: React.MouseEvent<HTMLVideoElement>) => void;
  fallbackSrc?: string;
  poster?: string;
  skeletonClassName?: string;
  errorClassName?: string;
  rootMargin?: string;
  threshold?: number;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  playsInline?: boolean;
  preload?: 'none' | 'metadata' | 'auto';
  onLoadStart?: () => void;
  onLoadComplete?: (loadTime: number) => void;
  onLoadError?: () => void;
  onInView?: () => void;
  onOutOfView?: () => void;
  videoMetadata?: {
    type?: string;
    duration?: number;
    fileSize?: number;
  };
}

/**
 * LazyVideo Component - Sprint 18 Phase 3
 * 
 * Lazy loading video component with Intersection Observer that only loads video metadata
 * when entering viewport. Implements progressive video loading strategy:
 * poster → metadata → full video
 * 
 * Features:
 * - Intersection Observer for viewport detection
 * - Progressive loading stages (poster → metadata → full video)
 * - Smooth loading transitions
 * - Performance tracking and metrics
 * - Fallback handling for failed loads
 * - Video-specific optimizations
 */
const LazyVideo: React.FC<LazyVideoProps> = ({
  src,
  alt = 'Generated Video',
  className,
  onError,
  onClick,
  fallbackSrc = '/placeholder-video.svg',
  poster,
  skeletonClassName,
  errorClassName,
  rootMargin = '100px', // Larger margin for videos
  threshold = 0.1,
  autoPlay = false,
  muted = true,
  loop = false,
  playsInline = true,
  preload = 'none', // Start with no preloading
  onLoadStart,
  onLoadComplete,
  onLoadError,
  onInView,
  onOutOfView,
  videoMetadata,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingStage, setLoadingStage] = useState<'skeleton' | 'poster' | 'metadata' | 'full'>('skeleton');
  const [loadStartTime, setLoadStartTime] = useState<number>(0);
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !isInView) {
          setIsInView(true);
          setIsLoading(true);
          setLoadStartTime(performance.now());
          setLoadingStage('poster');
          onInView?.();
          onLoadStart?.();
          
          // Start progressive loading sequence
          setTimeout(() => {
            if (videoRef.current) {
              setLoadingStage('metadata');
              // Load metadata first
              videoRef.current.preload = 'metadata';
            }
          }, 100);
          
          observer.disconnect(); // Stop observing once in view
        } else if (!entry.isIntersecting && isInView) {
          onOutOfView?.();
        }
      },
      {
        rootMargin,
        threshold,
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [rootMargin, threshold, isInView, onInView, onOutOfView, onLoadStart]);

  // Handle video metadata loaded
  const handleLoadedMetadata = () => {
    if (import.meta.env.DEV) {
      console.log(`[LazyVideo] Metadata loaded for ${src}`);
    }
    setLoadingStage('full');
    
    // Now allow full video loading if needed
    if (videoRef.current && (autoPlay || preload === 'auto')) {
      videoRef.current.preload = 'auto';
    }
  };

  // Handle video can play (enough data loaded)
  const handleCanPlay = () => {
    const loadTime = performance.now() - loadStartTime;
    setIsLoaded(true);
    setIsLoading(false);
    setHasError(false);
    onLoadComplete?.(loadTime);
    
    if (import.meta.env.DEV) {
      console.log(`[LazyVideo] Video ready to play: ${src} (${loadTime.toFixed(2)}ms)`);
    }
  };

  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    setHasError(true);
    setIsLoading(false);
    onLoadError?.();

    // Try fallback video
    if (videoRef.current && videoRef.current.src !== fallbackSrc) {
      videoRef.current.src = fallbackSrc;
      return;
    }

    // Call custom error handler if provided
    if (onError) {
      onError(e);
    }
  };

  const handleVideoClick = (e: React.MouseEvent<HTMLVideoElement>) => {
    if (onClick && (isLoaded || loadingStage === 'full')) {
      onClick(e);
    }
  };

  // Progressive loading indicator text
  const getLoadingText = () => {
    switch (loadingStage) {
      case 'poster': return 'Loading poster...';
      case 'metadata': return 'Loading metadata...';
      case 'full': return 'Loading video...';
      default: return 'Loading...';
    }
  };

  return (
    <div 
      ref={containerRef}
      className={cn("relative w-full h-full", className)}
    >
      {/* Show skeleton while not in view */}
      {!isInView && (
        <VideoSkeleton 
          className={cn("absolute inset-0 w-full h-full", skeletonClassName)}
          showPlayIcon={true}
          aspectRatio="video"
        />
      )}

      {/* Show error state */}
      {hasError && isInView && (
        <div 
          className={cn(
            "absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg",
            errorClassName
          )}
        >
          <div className="text-center p-4">
            <AlertTriangle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-xs text-gray-500">Failed to load video</p>
            {videoMetadata?.type && (
              <p className="text-xs text-gray-400 mt-1">{videoMetadata.type}</p>
            )}
          </div>
        </div>
      )}

      {/* Progressive loading indicator */}
      {isInView && isLoading && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20">
          <div className="bg-black/60 text-white text-xs px-3 py-2 rounded-lg flex items-center gap-2">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            {getLoadingText()}
          </div>
        </div>
      )}

      {/* Video metadata display */}
      {isInView && videoMetadata && (loadingStage === 'metadata' || loadingStage === 'full') && (
        <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
          {videoMetadata.duration && `${videoMetadata.duration}s`}
          {videoMetadata.type && ` • ${videoMetadata.type}`}
        </div>
      )}

      {/* Actual video - only render when in view */}
      {isInView && (
        <video
          ref={videoRef}
          src={src}
          className={cn(
            "w-full h-full object-cover transition-opacity duration-500",
            isLoaded ? "opacity-100" : "opacity-0"
          )}
          autoPlay={autoPlay}
          muted={muted}
          loop={loop}
          playsInline={playsInline}
          preload="none" // Start with no preloading, upgrade progressively
          poster={poster}
          onLoadedMetadata={handleLoadedMetadata}
          onCanPlay={handleCanPlay}
          onError={handleVideoError}
          onClick={handleVideoClick}
          title={alt}
        >
          Your browser does not support the video tag.
        </video>
      )}

      {/* Play icon overlay for non-autoplay videos */}
      {isInView && !autoPlay && !isLoading && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="bg-black/40 rounded-full p-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Play className="h-8 w-8 text-white fill-current" />
          </div>
        </div>
      )}


    </div>
  );
};

export default LazyVideo;
