import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { cn } from '@/lib/utils';
import LazyVideo from '@/components/LazyVideo';
import { useProgressiveVideoUrl } from '@/hooks/useProgressiveVideoUrl';

interface VideoWithLazyLoadingProps {
  s3_key: string;
  fallbackUrl: string;
  alt?: string;
  className?: string;
  onError?: (event: React.SyntheticEvent<HTMLVideoElement, Event>) => void;
  onClick?: (event: React.MouseEvent<HTMLVideoElement>) => void;
  showRetryButton?: boolean;
  showConnectionStatus?: boolean;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  poster?: string;
  rootMargin?: string;
  threshold?: number;
  onLoadComplete?: () => void;
  onLoadError?: () => void;
  onInView?: () => void;
  onOutOfView?: () => void;
  videoMetadata?: {
    type?: 'text-to-video' | 'multi-shot-auto' | 'multi-shot-manual';
    duration?: number;
    fileSize?: number;
  };
}

/**
 * VideoWithLazyLoading Component - Sprint 18 Phase 3
 * 
 * Combines progressive URL loading (Phase 2) with lazy loading (Phase 3) for optimal
 * video loading performance. Only loads videos when they enter the viewport and uses
 * cached/fallback URLs for immediate display.
 * 
 * Features:
 * - Lazy loading with Intersection Observer
 * - Progressive URL enhancement in background
 * - Immediate display with cached/fallback URLs
 * - Progressive video loading (poster → metadata → full video)
 * - Retry mechanisms with user-friendly UI
 * - Performance tracking and metrics
 */
const VideoWithLazyLoading: React.FC<VideoWithLazyLoadingProps> = ({
  s3_key,
  fallbackUrl,
  alt = 'Generated Video',
  className,
  onError,
  onClick,
  showRetryButton = true,
  showConnectionStatus = false,
  autoPlay = false,
  muted = true,
  loop = false,
  poster,
  rootMargin = '100px',
  threshold = 0.1,
  onLoadComplete,
  onLoadError,
  onInView,
  onOutOfView,
  videoMetadata,
}) => {
  const [videoLoadError, setVideoLoadError] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [hasEnteredView, setHasEnteredView] = useState(false);

  // Progressive URL loading - only start when video enters viewport
  const {
    url,
    isLoading: isUrlLoading,
    hasError: hasUrlError,
    isUsingFallback,
    refreshUrl,
    retryCount,
    videoMetadata: cachedVideoMetadata,
  } = useProgressiveVideoUrl({
    s3_key,
    fallbackUrl,
    enableRefresh: hasEnteredView, // Only enable URL refresh when in view
    refreshTimeout: 8000,
    maxRetries: 2,
    retryDelay: 1000,
    videoMetadata,
  });

  // Handle video entering viewport
  const handleInView = useCallback(() => {
    if (!hasEnteredView) {
      setHasEnteredView(true);
      if (import.meta.env.DEV) {
        console.log(`[VideoWithLazyLoading] Video entered viewport: ${s3_key}`);
      }
    }
    onInView?.();
  }, [hasEnteredView, s3_key, onInView]);

  // Handle video leaving viewport
  const handleOutOfView = useCallback(() => {
    onOutOfView?.();
  }, [onOutOfView]);

  // Handle video load error
  const handleVideoError = useCallback((event: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    setVideoLoadError(true);
    onLoadError?.();
    onError?.(event);
  }, [onLoadError, onError]);

  // Handle video load success
  const handleVideoLoad = useCallback((loadTime?: number) => {
    setVideoLoadError(false);
    onLoadComplete?.();
  }, [onLoadComplete]);

  // Handle retry button click
  const handleRetry = useCallback(async () => {
    setIsRetrying(true);
    setVideoLoadError(false);
    
    try {
      await refreshUrl();
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  }, [refreshUrl]);

  // Use the best available URL (progressive enhancement)
  const videoUrl = url || fallbackUrl;
  const combinedVideoMetadata = cachedVideoMetadata || videoMetadata;

  return (
    <div className={cn("relative group", className)}>
      <LazyVideo
        src={videoUrl}
        alt={alt}
        className="w-full h-full"
        fallbackSrc={fallbackUrl}
        poster={poster}
        rootMargin={rootMargin}
        threshold={threshold}
        autoPlay={autoPlay}
        muted={muted}
        loop={loop}
        playsInline={true}
        preload="none" // Lazy loading - only load when in viewport
        onError={handleVideoError}
        onClick={onClick}
        onLoadComplete={handleVideoLoad}
        onLoadError={onLoadError}
        onInView={handleInView}
        onOutOfView={handleOutOfView}
        videoMetadata={combinedVideoMetadata}
      />

      {/* Progressive URL Enhancement Indicator */}
      {hasEnteredView && isUrlLoading && !isUsingFallback && (
        <div className="absolute top-2 left-2 bg-blue-500/80 text-white text-xs px-2 py-1 rounded">
          Enhancing...
        </div>
      )}

      {/* Connection Status Indicator */}
      {showConnectionStatus && hasEnteredView && (
        <div className="absolute top-2 right-2 flex items-center space-x-1">
          {isUsingFallback ? (
            <WifiOff className="h-4 w-4 text-orange-500" title="Using fallback URL" />
          ) : (
            <Wifi className="h-4 w-4 text-green-500" title="Using fresh URL" />
          )}
        </div>
      )}

      {/* Error State with Retry */}
      {(videoLoadError || hasUrlError) && showRetryButton && hasEnteredView && (
        <div className="absolute inset-0 bg-black/60 flex flex-col items-center justify-center text-white">
          <div className="text-center">
            <p className="text-sm mb-3">Failed to load video</p>
            {retryCount > 0 && (
              <p className="text-xs text-gray-300 mb-3">
                Retry attempt: {retryCount}
              </p>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              disabled={isRetrying}
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Retrying...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </>
              )}
            </Button>
          </div>
        </div>
      )}


    </div>
  );
};

export default VideoWithLazyLoading;
