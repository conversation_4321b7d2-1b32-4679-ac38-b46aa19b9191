import { Wand2, Video } from "lucide-react";

// Video showcase data
const VIDEO_MODELS = [
  {
    id: 'basic',
    name: 'Basic Model',
    badgeClass: 'px-4 py-2 bg-black/60 backdrop-blur-sm text-white rounded-full text-sm font-medium',
    containerClass: 'relative aspect-video overflow-hidden rounded-2xl shadow-2xl',
    quality: { resolution: '720p', fps: '24fps' },
    isComingSoon: false
  },
  {
    id: 'pro',
    name: 'Pro Model',
    badgeClass: 'px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-500 text-white rounded-full text-sm font-medium',
    containerClass: 'relative aspect-video overflow-hidden rounded-2xl shadow-2xl ring-1 ring-purple-500/20',
    quality: { resolution: '1080p', fps: '30fps' },
    isComingSoon: true
  }
] as const;

const SHOWCASE_VIDEOS = [
  { src: "src/Videos/aws-video.mp4", model: 'basic', description: "Standard quality, optimized for speed" },
  { src: "src/Videos/google-video.mp4", model: 'pro', description: "Cinema-quality with advanced motion & effects" },
  { src: "src/Videos/aws-costal-road.mp4", model: 'basic', description: "Explore diverse scenes with our reliable basic model" },
  { src: "src/Videos/google-costal-road.mp4", model: 'pro', description: "Unlock dynamic storytelling with Pro video features" }
] as const;

// Reusable video card component
interface VideoCardProps {
  src: string;
  modelId: 'basic' | 'pro';
  description: string;
}

const VideoCard = ({ src, modelId, description }: VideoCardProps) => {
  const model = VIDEO_MODELS.find(m => m.id === modelId);
  if (!model) return null;

  return (
    <div className="group">
      <div className={model.containerClass}>
        <video
          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-[1.02]"
          controls
          preload="metadata"
        >
          <source src={src} type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Quality Badge */}
        <div className="absolute top-6 left-6 z-10 flex gap-3">
          <span className={model.badgeClass}>
            {model.name}
          </span>
          {model.isComingSoon && (
            <span className="px-3 py-1 bg-amber-500/20 backdrop-blur-sm text-amber-300 rounded-full text-sm font-medium">
              Coming Soon
            </span>
          )}
        </div>

        {/* Quality Indicators */}
        <div className="absolute bottom-6 left-6 flex gap-3">
          <span className="px-3 py-1 bg-black/60 backdrop-blur-sm text-white text-sm rounded-lg">
            {model.quality.resolution}
          </span>
          <span className="px-3 py-1 bg-black/60 backdrop-blur-sm text-white text-sm rounded-lg">
            {model.quality.fps}
          </span>
        </div>
      </div>

      {/* Description */}
      <div className="mt-6 text-center">
        <p className="text-white/70 text-lg">{description}</p>
      </div>
    </div>
  );
};

const VideoShowcase = () => {
  return (
    <div className="mt-24 w-full" data-section="video-generation">
      {/* Feature header - Colorful */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center gap-3 mb-4">
          <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-violet-500/20 to-purple-500/20 flex items-center justify-center">
            <Video className="w-5 h-5 text-violet-400" />
          </div>
          <h3 className="text-2xl md:text-3xl font-medium bg-gradient-to-r from-violet-300 to-purple-300 bg-clip-text text-transparent">Video Generation</h3>
        </div>
        <p className="text-white/80 text-lg max-w-2xl mx-auto">
          AI-powered video creation from text prompts
        </p>
      </div>

      {/* Video Comparison Grid - Full Width Cinematic */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
        {SHOWCASE_VIDEOS.map((video, index) => (
          <VideoCard
            key={index}
            src={video.src}
            modelId={video.model}
            description={video.description}
          />
        ))}
      </div>

      {/* Minimal Feature Highlights */}
      <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
        <div>
          <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-blue-600/10 to-cyan-600/10 flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h4 className="text-lg font-medium text-white mb-2">Lightning Fast</h4>
          <p className="text-white/60">Generate videos in under 60 seconds</p>
        </div>
        <div>
          <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-purple-600/10 to-pink-600/10 flex items-center justify-center">
            <Wand2 className="w-6 h-6 text-purple-400" />
          </div>
          <h4 className="text-lg font-medium text-white mb-2">Smart Scenes</h4>
          <p className="text-white/60">AI understands context and timing</p>
        </div>
        <div>
          <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-gradient-to-br from-green-600/10 to-emerald-600/10 flex items-center justify-center">
            <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h4 className="text-lg font-medium text-white mb-2">Export Ready</h4>
          <p className="text-white/60">Multiple formats for any platform</p>
        </div>
      </div>
    </div>
  );
};

export default VideoShowcase;