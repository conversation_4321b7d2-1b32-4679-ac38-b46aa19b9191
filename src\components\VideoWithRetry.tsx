import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, Wifi, WifiOff, Play, Pause, Volume2, VolumeX } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useProgressiveVideoUrl } from '@/hooks/useProgressiveVideoUrl';
import VideoSkeleton from '@/components/VideoSkeleton';

interface VideoWithRetryProps {
  s3_key: string;
  fallbackUrl: string;
  alt?: string;
  className?: string;
  onError?: (event: React.SyntheticEvent<HTMLVideoElement, Event>) => void;
  onClick?: (event: React.MouseEvent<HTMLVideoElement>) => void;
  showRetryButton?: boolean;
  showConnectionStatus?: boolean;
  showPlaybackControls?: boolean;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  preload?: 'none' | 'metadata' | 'auto';
  poster?: string;
  onLoadComplete?: () => void;
  onLoadError?: () => void;
  onInView?: () => void;
  onOutOfView?: () => void;
  videoMetadata?: {
    type?: 'text-to-video' | 'multi-shot-auto' | 'multi-shot-manual';
    duration?: number;
    fileSize?: number;
  };
}

/**
 * VideoWithRetry Component - Sprint 18 Phase 2
 * 
 * Progressive loading video component with immediate display using cached/fallback URLs
 * and background enhancement with fresh presigned URLs. Based on the successful ImageWithRetry
 * pattern from Sprint 17.
 * 
 * Features:
 * - Immediate display with cached or fallback URLs
 * - Background refresh of fresh presigned URLs
 * - Retry mechanisms with user-friendly UI
 * - Progressive loading states and indicators
 * - Video-specific controls and metadata
 * - Intersection Observer for lazy loading (future Phase 3)
 */
const VideoWithRetry: React.FC<VideoWithRetryProps> = ({
  s3_key,
  fallbackUrl,
  alt = 'Generated Video',
  className,
  onError,
  onClick,
  showRetryButton = true,
  showConnectionStatus = false,
  showPlaybackControls = true,
  autoPlay = false,
  muted = true,
  loop = false,
  preload = 'metadata',
  poster,
  onLoadComplete,
  onLoadError,
  onInView,
  onOutOfView,
  videoMetadata,
}) => {
  const [videoLoadError, setVideoLoadError] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(muted);
  const [showControls, setShowControls] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const {
    url,
    isLoading: isUrlLoading,
    hasError: hasUrlError,
    isUsingFallback,
    refreshUrl,
    retryCount,
    videoMetadata: cachedVideoMetadata,
  } = useProgressiveVideoUrl({
    s3_key,
    fallbackUrl,
    enableRefresh: true,
    refreshTimeout: 8000, // Longer timeout for videos
    maxRetries: 2,
    retryDelay: 1000,
    videoMetadata,
  });

  // Handle video load error
  const handleVideoError = (event: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    setVideoLoadError(true);
    onLoadError?.();
    onError?.(event);
  };

  // Handle video load success
  const handleVideoLoad = () => {
    setVideoLoadError(false);
    onLoadComplete?.();
  };

  // Handle retry button click
  const handleRetry = async () => {
    setIsRetrying(true);
    setVideoLoadError(false);
    
    try {
      await refreshUrl();
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  // Video playback controls
  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  // Handle video events
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleVolumeChange = () => setIsMuted(video.muted);

    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('volumechange', handleVolumeChange);

    return () => {
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('volumechange', handleVolumeChange);
    };
  }, []);

  // Show loading skeleton while URL is being fetched
  if (isUrlLoading && isUsingFallback && !url) {
    return <VideoSkeleton className={className} />;
  }

  return (
    <div 
      className={cn("relative group", className)}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      {/* Video Element */}
      <video
        ref={videoRef}
        src={url}
        className="w-full h-full object-cover"
        autoPlay={autoPlay}
        muted={isMuted}
        loop={loop}
        playsInline
        preload={preload}
        poster={poster}
        onError={handleVideoError}
        onLoadedData={handleVideoLoad}
        onClick={onClick}
        title={alt}
      >
        Your browser does not support the video tag.
      </video>

      {/* Progressive Loading Indicator */}
      {isUrlLoading && !isUsingFallback && (
        <div className="absolute top-2 left-2 bg-blue-500/80 text-white text-xs px-2 py-1 rounded">
          Enhancing...
        </div>
      )}

      {/* Connection Status Indicator */}
      {showConnectionStatus && (
        <div className="absolute top-2 right-2 flex items-center space-x-1">
          {isUsingFallback ? (
            <WifiOff className="h-4 w-4 text-orange-500" title="Using fallback URL" />
          ) : (
            <Wifi className="h-4 w-4 text-green-500" title="Using fresh URL" />
          )}
        </div>
      )}

      {/* Video Metadata Display */}
      {cachedVideoMetadata && showControls && (
        <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
          {cachedVideoMetadata.duration && `${cachedVideoMetadata.duration}s`}
          {cachedVideoMetadata.type && ` • ${cachedVideoMetadata.type}`}
        </div>
      )}

      {/* Playback Controls */}
      {showPlaybackControls && showControls && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex items-center space-x-2 bg-black/60 rounded-lg px-3 py-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={togglePlayPause}
              className="text-white hover:text-white hover:bg-white/20"
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMute}
              className="text-white hover:text-white hover:bg-white/20"
            >
              {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      )}

      {/* Error State with Retry */}
      {(videoLoadError || hasUrlError) && showRetryButton && (
        <div className="absolute inset-0 bg-black/60 flex flex-col items-center justify-center text-white">
          <div className="text-center">
            <p className="text-sm mb-3">Failed to load video</p>
            {retryCount > 0 && (
              <p className="text-xs text-gray-300 mb-3">
                Retry attempt: {retryCount}
              </p>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              disabled={isRetrying}
              className="bg-white/10 border-white/20 text-white hover:bg-white/20"
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Retrying...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </>
              )}
            </Button>
          </div>
        </div>
      )}


    </div>
  );
};

export default VideoWithRetry;
